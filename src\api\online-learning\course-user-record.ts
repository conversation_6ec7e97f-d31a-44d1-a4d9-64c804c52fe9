import request from '@/config/axios'

export const OnlineLearningCourseUserRecord = {
  // 查询当前用户可学习课程列表-带状态-pageList
  coursePage: async (params) => {
    return await request.post({ url: `tb/course-user-record/chapter/page`, data: params })
  },
  // 查询课程章节列表-带状态-pageList
  chapterPage: async (params) => {
    return await request.post({ url: `tb/course-user-record/chapter/page`, data: params })
  },
  // 【课程】学习状态记录/更新
  courseAddOrUpdate: async (data) => {
    return await request.post({ url: `tb/course-user-record/course/addOrUpdate`, data })
  },
  // 【课程章节】学习状态记录/更新
  chapterAddOrUpdate: async (data) => {
    return await request.post({ url: `tb/course-user-record/chapter/addOrUpdate`, data })
  },
  // 【课程章节】详情
  chapterDetail: async (params) => {
    return await request.get({ url: `tb/course-user-record/chapter/detail`, params })
  }
}
