// 基础车辆信息接口
export interface BaseVehicleInfo {
  id: number
  vehicleId: string
  licensePlate: string
  frameNumber: string
  vehicleName: string
  vehicleModel: string
  year: string // 年份
  vehicleBrand: string // 品牌
  vehicleSeries: string // 车系
  vehicleImgeUrls: string[] // 图片
  expiryDate: string // 有效期截止日
  mileage: number // 行驶公里数
  initialRegistration: string // 初次注册年月
  passengerCapacity: number // 乘客容量
  remark: string // 备注
  useStatus: string // 状态
  attachmentUrls: string[] // 附件
}

// 车辆详细信息接口
export interface VehicleDetailInfo extends BaseVehicleInfo {
  mileage: number
  inspectionRecord: string
  registrationDate: string
  initialRegistration: string
  vehicleInspectionExpirationDate: string
  userName: string
  userAddress: string
  ownerName: string
  ownerAddress: string
  headquartersLocation: string
  vehicleModel: string
  createTime: string
  vehicleImgeUrls: string[]
  attachmentUrls: string[]
  useStatus: string
  nextRegularInspectionTime: string
  vehicleImgeUrl: string
  attachmentUrl: string
}

// 车辆分页响应接口
export interface VehiclePageResponse extends BaseVehicleInfo {
  assignList: DriverVehicleResponse[]
  mileage: number
  passengerCapacity: number
  vehicleSeries: string
  vehicleImage: string
}

// 车辆响应接口
export interface VehicleResponse extends VehicleDetailInfo {
  tenantId: number
}

// 车辆保存请求接口
export interface VehicleSaveRequest extends VehicleDetailInfo {
  tenantId: number
}

// 驾驶员车辆响应接口
export interface DriverVehicleResponse {
  id: number
  vehicleId: string
  userId: number
  userFirstName: string
  userLastName: string
}
