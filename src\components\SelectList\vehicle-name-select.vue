<template>
  <div>
    <el-select
      v-model="form.select"
      filterable
      remote
      :remote-method="handleRemoteSearch"
      placeholder="输入车辆名称进行搜索..."
      clearable
      v-lazy-load-dropdown="handleScrollDropdown"
      class="!w-full"
    >
      <el-option
        v-for="item in options"
        :key="item.id"
        :label="item.vehicleName"
        :value="item.vehicleName"
      />
    </el-select>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, nextTick } from 'vue'
import { VehicleApi } from '@/api/vehicle/basic'
import { BaseVehicleInfo } from '@/api/vehicle/basic/vo'

// 定义自定义指令
const vLazyLoadDropdown = {
  mounted(el, binding) {
    // 使用 MutationObserver 监听 DOM 变化
    const observer = new MutationObserver((mutations) => {
      const SELECT_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap')
      if (SELECT_DOM) {
        // 找到元素后添加事件监听
        SELECT_DOM.addEventListener('scroll', () => {
          if (SELECT_DOM.scrollTop + SELECT_DOM.clientHeight >= SELECT_DOM.scrollHeight) {
            binding.value()
          }
        })
        // 找到后停止观察
        observer.disconnect()
      }
    })

    // 开始观察 document.body 的子元素变化
    observer.observe(document.body, { childList: true, subtree: true })

    // 监听点击事件，当用户点击 select 时尝试添加滚动监听
    el.addEventListener('click', () => {
      nextTick(() => {
        const SELECT_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap')
        if (SELECT_DOM) {
          SELECT_DOM.addEventListener('scroll', () => {
            if (SELECT_DOM.scrollTop + SELECT_DOM.clientHeight >= SELECT_DOM.scrollHeight) {
              binding.value()
            }
          })
        }
      })
    })
  }
}

// 数据定义
const options = ref<BaseVehicleInfo[]>([])
const queryParams = reactive<any>({
  pageNo: 1,
  pageSize: 100,
  shopName: undefined,
  total: 0
})
const form = reactive<any>({
  select: ''
})

const getVehicleList = async () => {
  try {
    const data = await VehicleApi.getVehiclePage(queryParams)
    // 将新数据追加到现有数据中
    options.value = options.value.concat(data.list)
    queryParams.total = Math.ceil(data.total / queryParams.pageSize)
  } catch (error) {
    console.error('获取店铺列表失败:', error)
  }
}

// 远程搜索方法
const handleRemoteSearch = (query: string) => {
  if (query !== '') {
    // 重置分页参数
    queryParams.pageNo = 1
    options.value = []
    queryParams.vehicleName = query
    getVehicleList()
  } else {
    // 清空搜索条件时，重新加载全部数据
    queryParams.pageNo = 1
    options.value = []
    queryParams.vehicleName = undefined
    getVehicleList()
  }
}

// 滚动加载更多
const handleScrollDropdown = () => {
  debugger
  // 判断是否还有更多数据
  if (queryParams.pageNo >= queryParams.total) return
  // 页码加1
  queryParams.pageNo++
  // 获取下一页数据
  getVehicleList()
}

// 组件挂载时初始化数据
onMounted(() => {
  getVehicleList()
})

// 定义组件名称
defineOptions({
  name: 'VehicleNameSelect'
})

// 定义props和emits
const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  }
})

const emit = defineEmits(['update:modelValue', 'select'])

// 初始化form.select值
onMounted(() => {
  getVehicleList()
  // 初始化时设置form.select为props.modelValue
  form.select = props.modelValue
})

// 监听props.modelValue变化
watch(
  () => props.modelValue,
  (newVal) => {
    // 当外部modelValue变化时，更新form.select
    if (newVal !== form.select) {
      form.select = newVal
    }
  }
)

// 监听选择变化
watch(
  () => form.select,
  (newVal) => {
    emit('update:modelValue', newVal)
    const vehicleInfo = options.value.find((item) => item.vehicleName === newVal)
    if (vehicleInfo) {
      emit('select', vehicleInfo)
    }
  }
)
</script>

<style lang="scss" scoped></style>
