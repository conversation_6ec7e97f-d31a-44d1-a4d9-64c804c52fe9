<template>
  <el-dialog v-model="dialogVisible" height="80vh" width="80%" :before-close="handleClose">
    <div class="preview-container">
      <!-- 文件预览区域 -->
      <div class="preview-content">
        <img
          v-if="currentFileType === 'image'"
          :src="currentFile?.url"
          style="width: 100%; height: 60vh; object-fit: contain"
        />
        <iframe
          v-if="currentFileType === 'pdf'"
          :src="currentFile?.url"
          style="width: 100%; height: 60vh; border: none"
        ></iframe>
        <iframe
          v-if="currentFileType === 'office'"
          :src="officeViewerUrl"
          style="width: 100%; height: 60vh; border: none"
        ></iframe>
      </div>

      <!-- 缩略图列表 -->
      <div class="preview-thumbnails" v-if="files.length > 1">
        <div
          v-for="(file, index) in files"
          :key="index"
          class="thumbnail-item"
          :class="{ active: currentIndex === index }"
          @click="currentIndex = index"
        >
          <img v-if="file.type === 'image'" :src="file.url" />
          <div v-else class="file-icon">
            {{ file.type === 'pdf' ? 'PDF' : 'Office' }}
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface FileItem {
  url: string
  type: 'image' | 'pdf' | 'office'
}

const dialogVisible = ref(false)
const files = ref<FileItem[]>([])
const currentIndex = ref(0)

const fileType = computed(() => {
  if (files.value.length === 0) return 'image'
  return files.value[currentIndex.value].type
})

const currentFile = computed(() => {
  if (files.value.length === 0) return null
  return files.value[currentIndex.value]
})

const currentFileType = computed(() => {
  return currentFile.value?.type || 'image'
})

const officeViewerUrl = computed(() => {
  if (!currentFile.value) return ''
  return `https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(currentFile.value.url)}`
})

// 判断文件类型
const getFileType = (url: string): 'image' | 'pdf' | 'office' => {
  const ext = url.toLowerCase().split('.').pop()
  if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(ext!)) {
    return 'image'
  } else if (ext === 'pdf') {
    return 'pdf'
  } else if (['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(ext!)) {
    return 'office'
  }
  return 'image' // 默认作为图片处理
}

const open = (fileList: string[]) => {
  files.value = fileList.map((url) => ({
    url,
    type: getFileType(url)
  }))
  currentIndex.value = 0
  dialogVisible.value = true
}

const handleClose = () => {
  dialogVisible.value = false
  files.value = []
  currentIndex.value = 0
}

defineExpose({
  open
})
</script>

<style scoped>
.preview-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.preview-content {
  flex: 1;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.preview-thumbnails {
  display: flex;
  gap: 8px;
  padding: 8px;
  overflow-x: auto;
}

.thumbnail-item {
  width: 80px;
  height: 80px;
  border: 2px solid transparent;
  border-radius: 4px;
  cursor: pointer;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
}

.thumbnail-item.active {
  border-color: #409eff;
}

.thumbnail-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.file-icon {
  font-size: 14px;
  color: #666;
  text-align: center;
}
</style>
