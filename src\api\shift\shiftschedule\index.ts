import request from '@/config/axios'

// 排班 VO
export interface ShiftScheduleVO {
  id: number // 主键
  weekId: number // 周次
  roleCode: string // 角色
  personId: number // 人员id
  personInfo: {
    userId: number
    nickname: string
  } // 人员信息
  scheduleDate: Date // 排班日期
  dayOfWeek: number // 星期几(1-7)
  startTime: String // 开始时间
  endTime: String // 结束时间
  shiftType: string // 班次类型
  templateId: number // 使用的模板id
  durationHours: number // 工作时长(小时)
  status: string // 状态
  notes: string // 备注
}

// 排班 API
export const ShiftScheduleApi = {
  // 查询排班分页
  getSchedulePage: async (params: any) => {
    return await request.get({ url: `/shift/schedule/page`, params })
  },

  // 查询排班详情
  getSchedule: async (id: number) => {
    return await request.get({ url: `/shift/schedule/get?id=` + id })
  },

  // 新增排班
  createSchedule: async (data: any) => {
    return await request.post({ url: `/shift/schedule/create`, data })
  },

  // 修改排班
  updateSchedule: async (data: ShiftScheduleVO) => {
    return await request.put({ url: `/shift/schedule/update`, data })
  },

  // 删除排班
  deleteSchedule: async (id: number) => {
    return await request.delete({ url: `/shift/schedule/delete?id=` + id })
  },

  // 导出排班 Excel
  exportSchedule: async (params) => {
    return await request.download({ url: `/shift/schedule/export-excel`, params })
  },

  getScheduleListByWeekId: async (weekId: number): Promise<ShiftScheduleVO[]> => {
    return await request.get({ url: `/shift/schedule/list-by-week-id?weekId=` + weekId })
  },

  clearWeekSchedule: async (weekId: number) => {
    return await request.delete({ url: `/shift/schedule/clear-week-schedule?weekId=${weekId}` })
  }
}
