import request from '@/config/axios'

// 车辆信息 VO
export interface CarVO {
  id: number // 主键
  vehicleInfo: string // 车辆信息
  mileage: number // 行驶公里数
  inspectionRecord: string // 汽车检查证记录事项
  vehicleNumber: string // 车辆编号
  frameNumber: string // 车架号
  registrationDate: Date // 注册日期
  initialRegistration: Date // 初次注册年月
  expiryDate: Date // 有效期截止日
  userName: string // 使用者姓名或名称
  userAddress: string // 使用者住址
  headquartersLocation: string // 使用总部位置
  vehicleName: string // 车辆名称
  vehicleModel: string // 车型
}

// 车辆信息 API
export const CarApi = {
  // 查询车辆信息分页
  getCarPage: async (params: any) => {
    return await request.get({url: `/car/car/page`, params})
  },

  // 查询车辆信息详情
  getCar: async (id: number) => {
    return await request.get({url: `/car/car/get?id=` + id})
  },

  // 新增车辆信息
  createCar: async (data: CarVO) => {
    return await request.post({url: `/car/car/create`, data})
  },

  // 修改车辆信息
  updateCar: async (data: CarVO) => {
    return await request.put({url: `/car/car/update`, data})
  },

  // 删除车辆信息
  deleteCar: async (id: number) => {
    return await request.delete({url: `/car/car/delete?id=` + id})
  },

  // 导出车辆信息 Excel
  exportCar: async (params) => {
    return await request.download({url: `/car/car/export-excel`, params})
  }
}
