<template>
  <div>
    <el-select
      style="width: 100%"
      v-model="form.select"
      filterable
      remote
      :remote-method="handleRemoteSearch"
      clearable
      v-lazy-load-dropdown="handleScrollDropdown"
      class="!w-full"
      v-bind="$attrs"
    >
      <el-option
        v-for="item in options"
        :key="item.id"
        :label="displayType === 'nickname' ? item.nickname : item.id"
        :value="item[displayType]"
      />
    </el-select>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, nextTick, computed } from 'vue'
import { UserVO } from '@/api/system/user'
// import { DriverApi } from '@/api/driver'
import * as UserApi from '@/api/system/user'

// 定义自定义指令
const vLazyLoadDropdown = {
  mounted(el, binding) {
    // 使用 MutationObserver 监听 DOM 变化
    const observer = new MutationObserver((mutations) => {
      const SELECT_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap')
      if (SELECT_DOM) {
        // 找到元素后添加事件监听
        SELECT_DOM.addEventListener('scroll', () => {
          if (SELECT_DOM.scrollTop + SELECT_DOM.clientHeight >= SELECT_DOM.scrollHeight) {
            binding.value()
          }
        })
        // 找到后停止观察
        observer.disconnect()
      }
    })

    // 开始观察 document.body 的子元素变化
    observer.observe(document.body, { childList: true, subtree: true })

    // 监听点击事件，当用户点击 select 时尝试添加滚动监听
    el.addEventListener('click', () => {
      nextTick(() => {
        const SELECT_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap')
        if (SELECT_DOM) {
          SELECT_DOM.addEventListener('scroll', () => {
            if (SELECT_DOM.scrollTop + SELECT_DOM.clientHeight >= SELECT_DOM.scrollHeight) {
              binding.value()
            }
          })
        }
      })
    })
  }
}

// 数据定义
const options = ref<UserVO[]>([])
const queryParams = reactive<any>({
  pageNo: 1,
  pageSize: 100,
  nickname: undefined,
  total: 0
})
const form = reactive<any>({
  select: ''
})

// 获取用户列表数据
const getUserList = async () => {
  try {
    // 使用 getSimpleUserList 获取用户列表
    const data = await UserApi.getUserPage(queryParams)
    // 将新数据追加到现有数据中
    options.value = options.value.concat(data.list)
    queryParams.total = Math.ceil(data.total / queryParams.pageSize)
  } catch (error) {
    console.error('获取用户列表失败:', error)
  }
}

// 远程搜索方法
const handleRemoteSearch = (query: string) => {
  if (query !== '') {
    // 重置分页参数
    queryParams.pageNo = 1
    options.value = []
    queryParams.nickname = query
    getUserList()
  } else {
    // 清空搜索条件时，重新加载全部数据
    queryParams.pageNo = 1
    options.value = []
    queryParams.nickname = undefined
    getUserList()
  }
}

// 滚动加载更多
const handleScrollDropdown = () => {
  // 判断是否还有更多数据
  if (queryParams.pageNo >= queryParams.total) return
  // 页码加1
  queryParams.pageNo++
  // 获取下一页数据
  getUserList()
}

// 组件挂载时初始化数据
onMounted(() => {
  getUserList()
})

// 定义组件名称
defineOptions({
  name: 'UserSelect'
})

// 定义props和emits
const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  },
  type: {
    type: String,
    default: 'nickname' // 默认显示nickname，可选值：'nickname'或'id'
  }
})

// 计算属性：根据type决定显示nickname还是id
const displayType = computed(() => {
  return props.type === 'id' ? 'id' : 'nickname'
})

const emit = defineEmits(['update:modelValue', 'select'])

// 初始化form.select值
onMounted(() => {
  getUserList()
  // 初始化时设置form.select为props.modelValue
  form.select = props.modelValue
})

// 监听props.modelValue变化
watch(
  () => props.modelValue,
  (newVal) => {
    // 当外部modelValue变化时，更新form.select
    if (newVal !== form.select) {
      form.select = newVal
    }
  }
)

// 监听选择变化
watch(
  () => form.select,
  async (newVal) => {
    if(options.value.length === 0) {
      await getUserList()
    }
    // 找到选中的用户对象
    let selectedUser: any = {}
    if (props.type === 'id') {
      selectedUser = options.value.find((item) => item.id === newVal)
    }

    if (props.type === 'nickname') {
      selectedUser = options.value.find((item) => item.nickname === newVal)
    }
    
    // 如果是空值，不触发事件
    if (selectedUser === undefined) {
      emit('update:modelValue', '')
      return
    }
    // 触发update:modelValue事件
    emit('update:modelValue', props.type === 'id' ? selectedUser.id : selectedUser.nickname)
    if (selectedUser) {
      emit('select', selectedUser)
    }
  }
)
</script>

<style scoped>
/* 可以根据需要添加自定义样式 */
</style>
