<template>
  <div>
    <el-select
      v-model="form.select"
      filterable
      remote
      :remote-method="handleRemoteSearch"
      placeholder="输入车番进行搜索..."
      clearable
      v-lazy-load-dropdown="handleScrollDropdown"
      class="!w-full"
    >
      <el-option
        v-for="item in options"
        :key="item.id"
        :label="item.licensePlate"
        :value="item.licensePlate"
      />
    </el-select>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, nextTick } from 'vue'
import { VehicleApi } from '@/api/vehicle/basic'
import { BaseVehicleInfo } from '@/api/vehicle/basic/vo'

// 定义自定义指令
const vLazyLoadDropdown = {
  mounted(el: any, binding: any) {
    let scrollHandler: ((event: Event) => void) | null = null
    let dropdownElement: Element | null = null
    debugger
    // 滚动处理函数
    const handleScroll = () => {
      if (!dropdownElement) return

      // 使用更宽松的判断条件，避免精度问题
      const { scrollTop, clientHeight, scrollHeight } = dropdownElement as HTMLElement
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 5

      console.log('滚动事件触发', {
        scrollTop,
        clientHeight,
        scrollHeight,
        isAtBottom,
        threshold: scrollHeight - 5
      })

      if (isAtBottom) {
        console.log('到达底部，触发加载更多')
        binding.value()
      }
    }

    // 添加滚动监听器
    const addScrollListener = () => {
      // 查找下拉框滚动容器
      const selectDropdown = document.querySelector('.el-select-dropdown .el-select-dropdown__wrap')

      console.log('尝试添加滚动监听器', {
        found: !!selectDropdown,
        isDifferent: selectDropdown !== dropdownElement
      })

      if (selectDropdown && selectDropdown !== dropdownElement) {
        // 移除之前的监听器
        if (dropdownElement && scrollHandler) {
          console.log('移除之前的滚动监听器')
          dropdownElement.removeEventListener('scroll', scrollHandler)
        }

        // 设置新的元素和监听器
        dropdownElement = selectDropdown
        scrollHandler = handleScroll
        dropdownElement.addEventListener('scroll', scrollHandler, { passive: true })
        console.log('成功添加滚动监听器')
      }
    }

    // 使用 MutationObserver 监听下拉框的创建
    const observer = new MutationObserver(() => {
      addScrollListener()
    })

    // 开始观察 document.body 的子元素变化
    observer.observe(document.body, { childList: true, subtree: true })

    // 监听点击事件，确保下拉框打开时能添加滚动监听
    const clickHandler = () => {
      nextTick(() => {
        addScrollListener()
      })
    }

    el.addEventListener('click', clickHandler)

    // 保存清理函数到元素上，供 unmounted 使用
    el._cleanupLazyLoad = () => {
      observer.disconnect()
      el.removeEventListener('click', clickHandler)
      if (dropdownElement && scrollHandler) {
        dropdownElement.removeEventListener('scroll', scrollHandler)
      }
    }
  },

  unmounted(el: any) {
    // 清理资源
    if (el._cleanupLazyLoad) {
      el._cleanupLazyLoad()
      delete el._cleanupLazyLoad
    }
  }
}

// 数据定义
const options = ref<BaseVehicleInfo[]>([])
const queryParams = reactive<any>({
  pageNo: 1,
  pageSize: 50,
  shopName: undefined,
  total: 0
})
const form = reactive<any>({
  select: ''
})

const getVehicleList = async () => {
  try {
    const data = await VehicleApi.getVehiclePage(queryParams)
    // 将新数据追加到现有数据中
    options.value = options.value.concat(data.list)
    queryParams.total = Math.ceil(data.total / queryParams.pageSize)
  } catch (error) {
    console.error('获取店铺列表失败:', error)
  }
}

// 远程搜索方法
const handleRemoteSearch = (query: string) => {
  if (query !== '') {
    // 重置分页参数
    queryParams.pageNo = 1
    options.value = []
    queryParams.licensePlate = query
    getVehicleList()
  } else {
    // 清空搜索条件时，重新加载全部数据
    queryParams.pageNo = 1
    options.value = []
    queryParams.licensePlate = undefined
    getVehicleList()
  }
}

// 滚动加载更多
const handleScrollDropdown = () => {
  console.log('滚动触发加载更多', {
    currentPage: queryParams.pageNo,
    totalPages: queryParams.total,
    canLoadMore: queryParams.pageNo < queryParams.total
  })

  // 判断是否还有更多数据（当前页码小于总页数时才能加载更多）
  if (queryParams.pageNo >= queryParams.total) {
    console.log('已到达最后一页，无更多数据')
    return
  }

  // 页码加1
  queryParams.pageNo++
  console.log('加载第', queryParams.pageNo, '页数据')

  // 获取下一页数据
  getVehicleList()
}

// 组件挂载时初始化数据
onMounted(() => {
  getVehicleList()
})

// 定义组件名称
defineOptions({
  name: 'VehicleNameSelect'
})

// 定义props和emits
const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  }
})

const emit = defineEmits(['update:modelValue', 'select'])

// 初始化form.select值
onMounted(() => {
  getVehicleList()
  // 初始化时设置form.select为props.modelValue
  form.select = props.modelValue
})

// 监听props.modelValue变化
watch(
  () => props.modelValue,
  (newVal) => {
    // 当外部modelValue变化时，更新form.select
    if (newVal !== form.select) {
      form.select = newVal
    }
  }
)

// 监听选择变化
watch(
  () => form.select,
  (newVal) => {
    emit('update:modelValue', newVal)
    const vehicleInfo = options.value.find((item) => item.licensePlate === newVal)
    if (vehicleInfo) {
      emit('select', vehicleInfo)
    }
  }
)
</script>

<style lang="scss" scoped></style>
