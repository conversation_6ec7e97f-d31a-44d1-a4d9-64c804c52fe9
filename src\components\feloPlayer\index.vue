<template>
  <div :id="config.id"></div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeMount, watch } from 'vue'
import Player from 'xgplayer'
import 'xgplayer/dist/index.min.css'

defineOptions({
  name: 'FeloPlayer',
})

const props = defineProps({
  config: {
    type: Object,
    default() {
      return {
        id: 'mse',
        url: '',
      }
    },
  },
})

const player = ref<any>(null)

const emit = defineEmits(['player'])

const init = () => {
  if (props.config.url && props.config.url !== '') {
    player.value = new Player(props.config)
    
    if (props.config.disableProgress) {
      const playerEl = document.getElementById(props.config.id)
      if (playerEl) {
        playerEl.setAttribute('data-disable-progress', 'true')
      }
      
      player.value.on('ready', () => {
        const progressEl = playerEl?.querySelector('.xgplayer-progress') as HTMLElement
        if (progressEl) {
          progressEl.style.pointerEvents = 'none'
          
          const progressBtn = playerEl?.querySelector('.xgplayer-progress-btn') as HTMLElement
          if (progressBtn) {
            progressBtn.style.display = 'none'
          }
        }
      })
    }
    
    emit('player', player.value)
  }
}

const getPlayerEl = () => {
  return document.getElementById(props.config.id)
}

watch(
  props.config,
  () => {
    if (player.value && typeof player.value.destroy === 'function') {
      player.value.destroy()
    }
    init()
  },
  { deep: true }
)

onMounted(() => {
  init()
})

onBeforeMount(() => {
  player.value && typeof player.value.destroy === 'function' && player.value.destroy()
})

defineExpose({
  init,
  getPlayerEl
})
</script>

<style lang="scss">
.xgplayer {
  &-progress-played {
    background: var(--el-color-primary) !important;
  }

  &-progress-btn {
    background: var(--el-color-primary-light-9) !important;
    border: 0.5px solid var(--el-color-primary-light-9) !important;
    box-shadow: 0 0 1px var(--el-color-primary) !important;
  }

  &-progress-btn.active {
    border: 4px solid var(--el-color-primary-light-9) !important;
  }

  &-progress-btn.active:before {
    box-shadow: 0 0 3px var(--el-color-primary) !important;
  }

  .xg-options-list {
    li:hover,
    li.selected {
      color: var(--el-color-primary) !important;
    }
  }

  &-drag {
    background: var(--el-color-primary) !important;
  }
  
  /* 禁用进度条交互样式 */
  &[data-disable-progress="true"] {
    .xgplayer-progress {
      pointer-events: none !important;
    }
    
    .xgplayer-progress-btn {
      display: none !important;
    }
  }
}
</style>
