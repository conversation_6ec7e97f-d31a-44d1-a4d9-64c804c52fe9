import request from '@/config/axios'

// 汽车行程明细 VO
export interface CarItineraryDetailsVO {
  id: number // 主键
  carId: number // 汽车id
  date: Date // 行程日期
  sort: number // 行程排序
  departureTime: localtime // 出发时间
  departurePlace: string // 出发地
  destination: string // 目的地
  estimatedArrivalTime: localtime // 预计到达时间
  distance: number // 距离（公里）
  startRestTime: localtime // 开始休息时间
  endRestTime: localtime // 结束休息时间
  restLocation: string // 休息地点
}

// 汽车行程明细 API
export const CarItineraryDetailsApi = {
  // 查询汽车行程明细分页
  getCarItineraryDetailsPage: async (params: any) => {
    return await request.get({ url: `/itinerary/car-itinerary-details/page`, params })
  },

  // 查询汽车行程明细详情
  getCarItineraryDetails: async (id: number) => {
    return await request.get({ url: `/itinerary/car-itinerary-details/get?id=` + id })
  },

  // 新增汽车行程明细
  createCarItineraryDetails: async (data: CarItineraryDetailsVO) => {
    return await request.post({ url: `/itinerary/car-itinerary-details/create`, data })
  },

  // 修改汽车行程明细
  updateCarItineraryDetails: async (data: CarItineraryDetailsVO) => {
    return await request.put({ url: `/itinerary/car-itinerary-details/update`, data })
  },

  // 删除汽车行程明细
  deleteCarItineraryDetails: async (id: number) => {
    return await request.delete({ url: `/itinerary/car-itinerary-details/delete?id=` + id })
  },

  // 导出汽车行程明细 Excel
  exportCarItineraryDetails: async (params) => {
    return await request.download({ url: `/itinerary/car-itinerary-details/export-excel`, params })
  }
}