<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="localQueryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="90px"
    >
      <el-form-item label="车辆名称" prop="vehicleName">
        <vehicle-name-select class="!w-240px"  v-model="localQueryParams.vehicleName" />
      </el-form-item>
      <el-form-item label="车番" prop="licensePlate">
        <vehicle-license-plate-select class="!w-240px"  v-model="localQueryParams.licensePlate" />
        <!-- <el-input
          v-model.trim="localQueryParams.licensePlate"
          placeholder="请输入车牌号码"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        /> -->
      </el-form-item>

      <el-form-item label="品牌" prop="vehicleBrand">
        <el-select
          v-model="localQueryParams.vehicleBrand"
          placeholder="请选择品牌"
          clearable
          filterable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getDictOptions(DICT_TYPE.VEHICLE_BRAND)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="车系" prop="vehicleSeries">
        <el-select
          v-model="localQueryParams.vehicleSeries"
          placeholder="请选择车系"
          clearable
          filterable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getDictOptions(DICT_TYPE.VEHICLE_SERIES)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="车型" prop="vehicleModel">
        <el-select
          v-model="localQueryParams.vehicleModel"
          placeholder="请选择车型"
          clearable
          filterable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getDictOptions(DICT_TYPE.VEHICLE_MODEL)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="useStatus">
        <el-select
          v-model="localQueryParams.useStatus"
          placeholder="请选择状态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item class="float-right">
        <el-button type="primary" @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" /> 搜索
        </el-button>
        <el-button @click="resetQuery"> <Icon icon="ep:refresh" class="mr-5px" /> 重置 </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { DICT_TYPE, getDictOptions } from '@/utils/dict'
import VehicleNameSelect from '@/components/SelectList/vehicle-name-select.vue'
import VehicleLicensePlateSelect from '@/components/SelectList/vehicle-license-plate-select.vue'

defineOptions({ name: 'VehicleSearch' })

// 定义属性
const props = defineProps({
  queryParams: {
    type: Object,
    required: true
  },
  exportLoading: {
    type: Boolean,
    default: false
  }
})

// 定义事件
const emit = defineEmits(['update:queryParams', 'query', 'resetQuery', 'create', 'export'])

// 创建本地状态跟踪查询参数
const localQueryParams = computed({
  get: () => props.queryParams,
  set: (val) => emit('update:queryParams', val)
})

// 表单引用
const queryFormRef = ref()

// 搜索按钮操作
const handleQuery = () => {
  emit('query')
}

// 重置按钮操作
const resetQuery = () => {
  queryFormRef.value.resetFields()
  emit('resetQuery')
}

// 暴露方法给父组件
defineExpose({
  queryFormRef
})
</script>

<style scoped lang="scss"></style>
