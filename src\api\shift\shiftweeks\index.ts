import request from '@/config/axios'

// 周次 VO
export interface WeeksVO {
  id: number // 主键
  weekStartDate: Date // 周开始日期
  weekEndDate: Date // 周结束日期
  weekYear: number // 年份
  weekNumber: number // 第几周
  weekLabel: string // 周次标签
  isActive: boolean // 是否启用
}

// 周次 API
export const WeeksApi = {
  // 查询周次分页
  getWeeksPage: async (params: any) => {
    return await request.get({ url: `/shift/weeks/page`, params })
  },

  // 查询周次详情
  getWeeks: async (id: number) => {
    return await request.get({ url: `/shift/weeks/get?id=` + id })
  },

  // 新增周次
  createWeeks: async (data: WeeksVO) => {
    return await request.post({ url: `/shift/weeks/create`, data })
  },

  // 修改周次
  updateWeeks: async (data: WeeksVO) => {
    return await request.put({ url: `/shift/weeks/update`, data })
  },

  // 删除周次
  deleteWeeks: async (id: number) => {
    return await request.delete({ url: `/shift/weeks/delete?id=` + id })
  },

  // 导出周次 Excel
  exportWeeks: async (params) => {
    return await request.download({ url: `/shift/weeks/export-excel`, params })
  },

  // 获取最近三个月的周次
  getRecentThreeMonthsWeeks: async (): Promise<WeeksVO[]> => {
    return await request.get({ url: `/shift/weeks/recent-three-months` })
  }
}