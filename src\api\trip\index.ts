import request from '@/config/axios';

export interface TripSummaryVO {
  driverDuration: number; // 总行驶时间
  restDuration: number; // 总休息时间
  totalMileage: number; // 总里程
}

export interface TripDriverVO {
  id?: number;
  nickname?: number;
}

// 行程信息 VO
export interface TripVO {
  id: number; // 主键
  frameNumber: string;//车架号
  tripSummary: TripSummaryVO; // 行程统计信息
  checkResult: CheckResult[],
  licensePlate?: string;// 车牌照号
  checkResultCountMap: {},
  driverInfo?: TripDriverVO
}

// 行程信息 API
export const TripApi = {
  // 查询行程信息分页
  getTripPage: async (params: any) => {
    return await request.get({url: `/trip/list`, params});
  },

  // 查询行程信息详情
  getTrip: async (id: number) => {
    return await request.get({url: `/trip/${id}`});
  },

  // 新增行程信息
  createTrip: async (data: TripVO) => {
    return await request.post({url: `/trip/create`, data});
  },

  // 修改行程信息
  updateTrip: async (data: TripVO) => {
    return await request.put({url: `/trip/update`, data});
  },

  // 删除行程信息
  deleteTrip: async (id: number) => {
    return await request.delete({url: `/trip/delete?id=${id}`});
  },

  // 导出行程信息 Excel
  exportTrip: async (params: any) => {
    return await request.download({url: `/trip/export-excel`, params});
  }
};
