import request from '@/config/axios'

export const FileAcrhivesInit = {
  // 档案目录管理
  getVehicleDocumentClassTree: async (params) => {
    return await request.get({ url: `/tb/vehicle-document-template/tree`, params })
  },
  // 创建档案目录
  createVehicleDocumentClass: async (data) => {
    return await request.post({ url: `/tb/vehicle-document-template/create`, data })
  },
  // 删除车辆档案目录管理
  deleteVehicleDocumentClass: async (id) => {
    return await request.delete({ url: `/tb/vehicle-document-template/delete?id=${id}` })
  },
  // 修改车辆档案目录管理
  updateVehicleDocumentClass: async (data) => {
    return await request.put({ url: `/tb/vehicle-document-template/update`, data })
  },
  // 查看下级
  nextLevelSuperClass: async (params) => {
    return await request.get({ url: `/tb/vehicle-document-template/nextLevel`, params })
  },
  // 同步档案目录
  syncDocumentTemplate: async (params) => {
    return await request.put({ url: `/tb/vehicle-document-template/syncDocumentTemplate`, params })
  }, 
  // 车辆档案管理 分页
  getVehicleDocumentPage: async (params) => {
    return await request.get({ url: `/tb/vehicle-document/page`, params })
  },
  // 创建档案
  vehicleDocumentCreate: async (data) => {
    return await request.post({ url: `/tb/vehicle-document/create`, data })
  },
  // 删除档案
  vehicleDocumentDelete: async (id) => {
    return await request.delete({ url: `/tb/vehicle-document/delete?id=${id}` })
  }
}
