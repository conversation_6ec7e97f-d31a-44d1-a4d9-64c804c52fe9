// 批量
export interface VehicleMaintenanceConfigBatchSaveReqVO {
  maintenanceMileageInterval: number; // 预约公里间隔
  inspectionInterval: number; // 点检周期
  inspectionIntervalUnit: string; // 点检周期单位
  regularInspectionInterval: number; // 车检周期
  regularInspectionIntervalUnit: string; // 车检周期单位
  vehicleSeries: string; // 车系 批量保存时使用
}

// 基础车辆信息接口
export interface VehicleMaintenanceConfigSaveReqVO {
  id?: string; // 主键ID
  vehicleId: string; // 车辆ID
  maintenanceMileageInterval?: number; // 预约公里间隔
  inspectionInterval?: number; // 点检周期
  inspectionIntervalUnit?: string; // 点检周期单位
  regularInspectionInterval?: number; // 车检周期
  regularInspectionIntervalUnit?: string; // 车检周期单位
  vehicleSeries?: string; // 车系 批量保存时使用
}


// 车辆预约配置分页查询请求VO
export interface VehicleMaintenanceConfigPageReqVO {
  id?: string; // 主键ID
  vehicleId?: string; // 车辆ID
  vehicleName?: string; // 车辆名称
  vehicleBrand?: string; // 车辆品牌
  vehicleSeries?: string; // 车辆系列
  maintenanceMileageInterval?: number; // 预约公里间隔
  inspectionInterval?: number; // 点检周期
  inspectionIntervalUnit?: string; // 点检周期单位
  regularInspectionInterval?: number; // 车检周期
  regularInspectionIntervalUnit?: string; // 车检周期单位
}