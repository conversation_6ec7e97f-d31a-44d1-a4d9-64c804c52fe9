import request from '@/config/axios'

// etc爬虫记录 VO
export interface EtcRecordVO {
  id: number // 主键
  startTime: Date // 利用年月日 時分（自）
  endTime: Date // 利用年月日 時分（至）
  startLocation: string // 利用ＩＣ（自）
  endLocation: string // 利用ＩＣ（至）
  originalPrice: number // (割引前料金) 原始价格
  etcDiscountAmount: number // (ＥＴＣ割引額) ETC优惠金额
  tollFee: number // 通行料金 过路费
  vehicleType: string // 車種 车辆类型
  vehicleId: string // 車両番号
  etcCardNumber: string // ＥＴＣカード番号 ETC卡号
  remark: string // 备注
}

// etc爬虫记录 API
export const EtcRecordApi = {
  // 查询etc爬虫记录分页
  getEtcRecordPage: async (params: any) => {
    return await request.get({ url: `/bill/etc-record/page`, params })
  },

  // 查询etc爬虫记录详情
  getEtcRecord: async (id: number) => {
    return await request.get({ url: `/bill/etc-record/get?id=` + id })
  },

  // 新增etc爬虫记录
  createEtcRecord: async (data: EtcRecordVO) => {
    return await request.post({ url: `/bill/etc-record/create`, data })
  },

  // 修改etc爬虫记录
  updateEtcRecord: async (data: EtcRecordVO) => {
    return await request.put({ url: `/bill/etc-record/update`, data })
  },

  // 删除etc爬虫记录
  deleteEtcRecord: async (id: number) => {
    return await request.delete({ url: `/bill/etc-record/delete?id=` + id })
  },

  // 导出etc爬虫记录 Excel
  exportEtcRecord: async (params) => {
    return await request.download({ url: `/bill/etc-record/export-excel`, params })
  }
}