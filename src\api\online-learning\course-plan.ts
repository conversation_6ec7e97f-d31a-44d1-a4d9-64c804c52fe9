import request from '@/config/axios'

export const OnlineLearningCoursePlan = {
  // 课程计划树
  getCoursePlanTree: async (params) => {
    return await request.get({ url: `tb/course-plan/tree`, params })
  },
  // 创建课程计划
  createCoursePlan: async (data) => {
    return await request.post({ url: `tb/course-plan/create`, data })
  },
  // 删除课程计划
  deleteCoursePlan: async (id) => {
    return await request.delete({ url: `tb/course-plan/delete?id=${id}` })
  },
  // 修改课程计划
  updateCoursePlan: async (data) => {
    return await request.put({ url: `tb/course-plan/update`, data })
  },
  // 课程计划 分页
  getCoursePlanPage: async (params) => {
    return await request.get({ url: `tb/course-plan/page`, params })
  },
  // 查看下级
  nextLevelSuperClass: async (params) => {
    return await request.get({ url: `tb/course-plan/nextLevel`, params })
  },
  // 同步数据
  syncCoursePlan: async () => {
    return await request.put({ url: `tb/course-plan/syncCourse`, data: [] })
  }
}
