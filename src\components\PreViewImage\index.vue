<template>
  <el-dialog
    v-model="dialogVisible"
    width="80%"
    :before-close="handleClose"
  >
    <el-carousel :autoplay="false" indicator-position="outside">
      <el-carousel-item v-for="(image, index) in images" :key="index">
        <img :src="image" style="width: 100%; height: 100%; object-fit: contain" />
      </el-carousel-item>
    </el-carousel>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const dialogVisible = ref(false)
const images = ref<string[]>([])

const open = (imageList: string[]) => {
  images.value = imageList
  dialogVisible.value = true
}

const handleClose = () => {
  dialogVisible.value = false
}

defineExpose({
  open
})
</script>