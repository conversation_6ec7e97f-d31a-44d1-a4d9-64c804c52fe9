import request from '@/config/axios'

// 轮班模板 VO
export interface ShiftTemplateVO {
  id: number // 主键
  templateName: string // 模板名称
  startTime: String // 开始时间
  endTime: String // 结束时间
  durationHours: number // 工作时长(小时)
  isActive: boolean // 是否启用
}

// 轮班模板 API
export const ShiftTemplateApi = {
  // 查询轮班模板分页
  getTemplatePage: async (params: any) => {
    return await request.get({ url: `/shift/template/page`, params })
  },

  // 查询轮班模板详情
  getTemplate: async (id: number) => {
    return await request.get({ url: `/shift/template/get?id=` + id })
  },

  // 新增轮班模板
  createTemplate: async (data: ShiftTemplateVO) => {
    return await request.post({ url: `/shift/template/create`, data })
  },

  // 修改轮班模板
  updateTemplate: async (data: ShiftTemplateVO) => {
    return await request.put({ url: `/shift/template/update`, data })
  },

  // 删除轮班模板
  deleteTemplate: async (id: number) => {
    return await request.delete({ url: `/shift/template/delete?id=` + id })
  },

  // 导出轮班模板 Excel
  exportTemplate: async (params) => {
    return await request.download({ url: `/shift/template/export-excel`, params })
  }
}