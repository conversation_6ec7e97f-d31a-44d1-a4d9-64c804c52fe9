// 预约保养明细保存请求VO
export interface IAppointmentMaintenance {
  id?: string // 主键
  vehicleId: string // 车辆id
  maintenanceType: string // 保养类型（オイル交換,车辆修理，3月点检，12月定检）
  maintenanceTypeList?: string[] // 保养类型（オイル交換,车辆修理，3月点检，12月定检）
  repairItemsList?: string[] // 维修项目
  appointmentTime?: any // 预约时间
  appointer?: string // 预约者
  appointerRemark?: string // 预约者备注
  status?: string // 状态 （待保养-0，已完成-1，已取消-2）
  checkDecember?: boolean // 是否定检
  reservationAddressId?: string // 预约地址id
}

// 维修保养记录请求VO
export interface IMaintenanceDetail {
  id?: string // 主键
  vehicleId: string // 车辆id
  maintenanceType: string // 保养类型（オイル交換,车辆修理，3月点检，12月定检）
  maintainerTime: string // 实施日期
  maintenanceCost: number // 保养费用
  inspectionMileage: number // 出库公里数
  inboundMileage: number // 归库公里数
  maintainer: string // 保养者
  maintenanceRecord: string // 保养记录
  maintenanceRemark: string // 保养备注
  maintenanceFiles: string[] // 文件（如保养记录文件路径或url）
  maintenanceId?: string // 预约id
  maintenanceTypeList?: string[]
  compensationPhotoList?: string[]
  status: string // 状态
  isImplement?: boolean // 是否实施
}

// 预约保养明细分页查询请求VO
export interface IMaintenanceList {
  id: string // 主键
  vehicleId: string // 车辆id
  maintenanceId: string // 预约id
  maintenanceType: string // 保养类型（オイル交換,车辆修理，3月点检，12月定检）
  inspectionMileage: number // 本次保养里程数
  currentInspectionDate: string // 本次点检日期
  repairItems: string // 维修项目
  appointmentTime: string // 预约时间
  appointer: string // 预约者
  appointerRemark: string // 预约者备注
  maintainerTime: string // 保养时间
  maintainer: string // 保养者
  maintenanceCost: number // 保养费用
  maintenanceRecord: string // 保养记录
  maintenanceRemark: string // 保养备注
  maintenanceFiles: string[] // 文件（如保养记录文件路径或url）
  outboundMileage: number // 出库公里数
  inboundMileage: number // 归库公里数
  status: string // 状态
  checkDecember: boolean // 是否定检
  compensationPhotoList: string[] // 补照
  licensePlate?: string
}

export interface IMaintenanceCancel {
  id: string // 车辆id
  status: string // 状态
}

export interface IVehicleMaintenanceDetail {
  maintenanceId: string
  updateReqVOList: IAppointmentMaintenance[]
}

export interface IMaintenanceVerify {
  maintenanceId: string
  verifierId: number
}
