import request from '@/config/axios'

export const OnlineLearningCourseLearnResults = {
  // 查询课程学习结果-pageList
  page: async (data) => {
    return await request.post({ url: `tb/course-learn-results/page`, data })
  },
  // 【课程】阶段统计
  coursePhaseStatistic: async (data) => {
    return await request.post({ url: `tb/course-learn-results/course/phase/statistic`, data })
  },
  // 【课程】用户树
  courseUserTree: async (data) => {
    return await request.post({ url: `tb/course-learn-results/user/tree`, data })
  }
}
