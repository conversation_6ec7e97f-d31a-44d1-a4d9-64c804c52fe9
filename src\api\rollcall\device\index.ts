import request from '@/config/axios'

// 设备 VO
export interface DeviceVO {
  id: number // 主键
  regionCode: number // 设备区域id
  deviceKey: string // 设备序列号
  deviceName: string // 设备名
  deviceType: boolean // 设备类型
  versionCode: string // 版本号
  appVersion: number // app版本
  ip: string // IP地址
  wanMac: string // 设备mac地址
  wifiMac: string // 设备无线mac地址
  imei: string // imei
  channel: string // 设备渠道号
  secret: string // 云端秘钥
  commPwd: string // 通讯密码
  direction: string // 设备进出方向
  onlineFlag: boolean // 是否在线
  lastActiveTime: Date // 最后活跃时间
  personCount: number // 人员数
  photoCount: number // 照片数
  fingerCount: number // 指纹数
  palmCount: number // 掌纹数
  firmwareVersion: string // 固件版本
  faceAlgorithmVersion: string // 人脸算法版本
  manufacturer: string // 设备厂家
  os: string // 设备系统
  osVersion: string // 系统版本
  cpu: string // cpu型号
  ram: string // 运行内存大小
  rom: string // 存储内存大小
  freeDiskSpace: number // 磁盘剩余存储空间
  status: string // 状态
  remarks: string // 备注信息
}

export interface SetCallbackUrlVO {
  id: number
  deviceKey: string
  // 识别记录的url地址
  identifyUrl?: string
  // 设备心跳的url地址
  heartbeatUrl?: string
  // 注册人员数据的url地址
  imgRegUrl?: string
  // 报警数据的url地址
  alarmUrl?: string
}

// 设备 API
export const DeviceApi = {
  // 查询设备分页
  getDevicePage: async (params: any) => {
    return await request.get({ url: `/rollcall/device/page`, params })
  },

  // 查询设备详情
  getDevice: async (id: number) => {
    return await request.get({ url: `/rollcall/device/get?id=` + id })
  },

  // 新增设备
  createDevice: async (data: DeviceVO) => {
    return await request.post({ url: `/rollcall/device/create`, data })
  },

  // 修改设备
  updateDevice: async (data: DeviceVO) => {
    return await request.put({ url: `/rollcall/device/update`, data })
  },

  // 删除设备
  deleteDevice: async (id: number) => {
    return await request.delete({ url: `/rollcall/device/delete?id=` + id })
  },

  // 批量删除设备
  batchDeleteDevice: async (ids: number[]) => {
    return await request.delete({ url: `/rollcall/device/delete/batch?ids=` + ids.join(',') })
  },

  // 导出设备 Excel
  exportDevice: async (params) => {
    return await request.download({ url: `/rollcall/device/export-excel`, params })
  },

  // 设置回调url
  setCallbackUrl: async (data: SetCallbackUrlVO) => {
    return await request.post({ url: `/rollcall/device/callback-url`, data })
  },

  // 同步人员信息到点呼设备
  syncPersonInfoToDevices: async (devicesKey: string[], userIds: number[]) => {
    return await request.post({
      url: `/rollcall/device/sync-person-info-to-devices`,
      data: { devicesKey, userIds }
    })
  },

  // 设备照片注册
  faceUpload: async (devicesKey: string[], userIds: number[]) => {
    return await request.post({
      url: `/rollcall/device/face-upload`,
      data: { devicesKey, userIds }
    })
  },

  // 获取已分配给指定设备的司机列表
  getDistributedUserList: async (params) => {
    return await request.get({ url: `/rollcall/device/distributed-user-list`, params })
  },

  /**
   * 批量删除已经分配给点呼机的司机
   */
  deleteDistributedUser: async (deviceKey: string, userIds: number[]) => {
    return await request.delete({
      url: `/rollcall/device/delete-distributed-user`,
      data: {
        deviceKey,
        userIds
      }
    })
  }
}
