import request from '@/config/axios'
import {
  IAppointmentMaintenance,
  IMaintenanceList,
  IMaintenanceDetail,
  IMaintenanceCancel,
  IVehicleMaintenanceDetail,
  IMaintenanceVerify
} from './vo/reservation'

// 车辆基础信息 API
export const MaintenanceDetailApi = {
  // 批量创建车辆预约配置
  batchCreateMaintenanceConfig: async (params: IAppointmentMaintenance[]): Promise<void> => {
    return await request.post({ url: `/tb/vehicle-maintenance-detail/create-batch`, data: params })
  },

  // 批量创建车辆预约配置
  updateBatchMaintenanceConfig: async (params: IVehicleMaintenanceDetail): Promise<void> => {
    return await request.post({ url: `/tb/vehicle-maintenance-detail/update-batch`, data: params })
  },

  // 创建车辆预约配置
  createMaintenanceConfig: async (params: IAppointmentMaintenance): Promise<void> => {
    return await request.post({ url: `/tb/vehicle-maintenance-detail/create`, data: params })
  },

  // 更新车辆预约配置
  updateMaintenanceConfig: async (params: IMaintenanceDetail): Promise<void> => {
    return await request.put({ url: `/tb/vehicle-maintenance-detail/update`, data: params })
  },

  // 删除车辆预约配置
  deleteMaintenanceConfig: async (id: string): Promise<void> => {
    return await request.delete({ url: `/tb/vehicle-maintenance-detail/delete?id=${id}` })
  },

  // 获得车辆保养详细信息
  getMaintenanceConfig: async (id: string): Promise<IMaintenanceList> => {
    return await request.get({ url: `/tb/vehicle-maintenance-detail/get?id=${id}` })
  },

  // 获得车辆保养详细信息分页
  getMaintenanceConfigPage: async (
    params: Partial<IMaintenanceList> & { pageNo: number; pageSize: number }
  ): Promise<PageResult<IMaintenanceList[]>> => {
    return await request.post({ url: `/tb/vehicle-maintenance-detail/page`, data: params })
  },

  // 获得车辆保养详细信息详情
  getMaintenanceDetail: async (params: Partial<IMaintenanceList>): Promise<IMaintenanceList[]> => {
    return await request.get({ url: `/tb/vehicle-maintenance-detail/detail`, params })
  },

  // 获得车辆保养详细信息详情
  getMaintenanceById: async (params: Partial<IMaintenanceList>): Promise<IMaintenanceList[]> => {
    return await request.get({ url: `/tb/vehicle-maintenance-detail/getById`, params })
  },

  // 取消车辆预约
  cancelMaintenance: async (params: IMaintenanceCancel): Promise<void> => {
    return await request.post({ url: `/tb/vehicle-maintenance-detail/cancel`, data: params })
  },

   // 取消车辆预约
   selfCompensationVerify: async (params: IMaintenanceVerify): Promise<void> => {
    return await request.post({ url: `/tb/vehicle-maintenance-detail/verify`, data: params })
  }
  
}
