import request from '@/config/axios'
export interface DriverVO {
  // 司机id
  id: number
  // 司机名称
  nickname: string
  // 照片
  photoUrl: string
  // 头像
  avatar: string
}

export const DriverApi = {
  // 获取司机分页
  getDriverPage: async (params) => {
    return await request.get({ url: `/tb/driver/list`, params })
  },
  // 获取司机分页根据租户
  getDriverPageByTenantId: async (params) => {
    return await request.get({ url: `/tb/driver/tenant/list`, params })
  }
}
