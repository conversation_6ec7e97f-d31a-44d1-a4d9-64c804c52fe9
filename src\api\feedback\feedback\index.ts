import request from '@/config/axios'

// 问题反馈 VO
export interface FeedbackVO {
  id: number // 主键
  userId: number // 用户id
  type: string // 反馈类型
  content: string // 反馈内容
  images: string // 反馈图片
}

// 问题反馈 API
export const FeedbackApi = {
  // 查询问题反馈分页
  getFeedbackPage: async (params: any) => {
    return await request.get({ url: `/feedback/feedback/page`, params })
  },

  // 查询问题反馈详情
  getFeedback: async (id: number) => {
    return await request.get({ url: `/feedback/feedback/get?id=` + id })
  },

  // 新增问题反馈
  createFeedback: async (data: FeedbackVO) => {
    return await request.post({ url: `/feedback/feedback/create`, data })
  },

  // 修改问题反馈
  updateFeedback: async (data: FeedbackVO) => {
    return await request.put({ url: `/feedback/feedback/update`, data })
  },

  // 删除问题反馈
  deleteFeedback: async (id: number) => {
    return await request.delete({ url: `/feedback/feedback/delete?id=` + id })
  },

  // 导出问题反馈 Excel
  exportFeedback: async (params) => {
    return await request.download({ url: `/feedback/feedback/export-excel`, params })
  }
}