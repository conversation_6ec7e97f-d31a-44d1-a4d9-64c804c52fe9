import request from '@/config/axios'
import { UserVO } from '@/api/system/user'

// 汽车行程 VO
export interface VehicleTripVO {
  id: number // 主键
  vehicleId: string // 车辆
  vehicle: {
    licensePlate: string // 车牌照号
  }
  tripUserId: number // 司机
  outTime: Date // 出库时间
  backTime: Date // 归库时间
  outKm: number // 出库公里数
  backKm: number // 归库公里数
  highwayFee: number // 高速费
  fuelCost: number // 油费
  washingFee: number // 洗车费
  parkingFee: number // 停车费
  advancePayment: number // 垫付款
  checkResult: string // 点检结果
  confirmStatus: number // 确认状态(0已经提交（审核中），1审核通过，2审核不通过)
  totalDrivingTime: number // 总行驶时间（单位分钟）
  totalRestTime: number // 总休息时间（单位分钟）
  totalMileage: number // 总里程（单位km）
  departureTime: number // 出车时间（单位分钟）/**
  isDiscontinuous: number //行程是否不连续
  isChartered:number; // 是否是外宿
}

// 日报审批状态
export type TripReviewStatus = 'PENDING_REVIEW' | 'APPROVED' | 'REJECTED' | 'UN_KNOW'
// 汽车行程 API
export const VehicleTripApi = {
  // 查询汽车行程分页
  getVehicleTripPage: async (params: any) => {
    return await request.get({ url: `/daily/vehicle-trip/page`, params })
  },

  // 查询汽车行程详情
  getVehicleTrip: async (id: number) => {
    return await request.get({ url: `/daily/vehicle-trip/get?id=` + id })
  },

  // 新增汽车行程
  createVehicleTrip: async (data: VehicleTripVO) => {
    return await request.post({ url: `/daily/vehicle-trip/create`, data })
  },

  // 修改汽车行程
  updateVehicleTrip: async (data: VehicleTripVO) => {
    return await request.put({ url: `/daily/vehicle-trip/update`, data })
  },

  // 删除汽车行程
  deleteVehicleTrip: async (id: number) => {
    return await request.delete({ url: `/daily/vehicle-trip/delete?id=` + id })
  },

  // 导出汽车行程 Excel
  exportVehicleTrip: async (params) => {
    return await request.download({ url: `/daily/vehicle-trip/export-excel`, params })
  },

  getUserListByCode: async (code: string): Promise<UserVO[]> => {
    return await request.get({ url: `/daily/vehicle-trip/manager/list/${code}` })
  },
  getDailyManagerList: async (): Promise<UserVO[]> => {
    return await request.get({ url: `/daily/vehicle-trip/manager/list` })
  },

  // 通过/合格日报
  approvedTrip: async (
    tripIdList: number[], // 日报ID列表
    approveUserId: number | null
  ) => {
    return await request.post({
      url: `/daily/vehicle-trip/review/approved`,
      data: {
        tripIdList,
        approveUserId
      }
    })
  },

  // 退回日报
  rejectTrip: async (
    tripIdList: number[] // 日报ID列表
  ) => {
    return await request.post({ url: `/daily/vehicle-trip/review/rejected`, data: tripIdList })
  },

  exportDaily: async (vehicleId: string, outTimes: Date[]) => {
    return await request.download({
      url: `/daily/vehicle-trip/export-daily?vehicleId=${vehicleId}&outTimes=${new Date(outTimes[0]).toISOString().split('.')[0]}&outTimes=${new Date(outTimes[1]).toISOString().split('.')[0]}`
    })
  }
}
