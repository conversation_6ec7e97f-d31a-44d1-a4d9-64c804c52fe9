import request from '@/config/axios'
import { VehiclePageResponse, VehicleResponse, VehicleSaveRequest } from './vo'

// 车辆基础信息 API
export const VehicleApi = {
  // 查询车辆基础信息分页
  getVehiclePage: async (params: any): Promise<PageResult<VehiclePageResponse[]>> => {
    return await request.post({ url: `/tb/vehicle/page`, data: params })
  },
  // 查询车辆基础信息分页
  getVehicleSimplList: async (params: any): Promise<PageResult<VehicleVO[]>> => {
    return await request.get({ url: `/tb/vehicle/simple-list`, params: params })
  },

  // 设置是否允许在日报之间插入指定车辆的日报
  setDailyInsertStatus: async (vehicleIds: string[], enable: boolean) => {
    return await request.post({
      url: `/tb/vehicle/daily-insert-status`,
      data: { vehicleIds, enable }
    })
  },

  // 查询车辆基础信息详情
  getVehicle: async (id: string): Promise<VehicleResponse> => {
    return await request.get({ url: `/tb/vehicle/get?vehicleId=` + id })
  },

  // 新增车辆基础信息
  createVehicle: async (data: VehicleSaveRequest): Promise<void> => {
    return await request.post({ url: `/tb/vehicle/create`, data })
  },

  // 修改车辆基础信息
  updateVehicle: async (data: VehicleSaveRequest): Promise<void> => {
    return await request.put({ url: `/tb/vehicle/update`, data })
  },

  // 删除车辆基础信息
  deleteVehicle: async (vehicleId: number): Promise<void> => {
    return await request.delete({ url: `/tb/vehicle/delete?vehicleId=` + vehicleId })
  },

  // 批量修改车辆驾驶员
  batchModifyDriver: async (params: any): Promise<void> => {
    return await request.post({ url: `/tb/driver-vehicle/batch-modify`, data: params })
  },

  // 预约
  getAppointment: async (params: any): Promise<void> => {
    return await request.post({ url: `/tb/vehicle/appointment`, data: params })
  }
}
