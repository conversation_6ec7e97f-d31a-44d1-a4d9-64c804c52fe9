import request from '@/config/axios'

export const OnlineLearningCoursePublish = {
  // 课程发布
  create: async (data) => {
    return await request.post({ url: `tb/course-publish/create`, data })
  },
  // 课程发布-更新
  update: async (data) => {
    return await request.put({ url: `tb/course-publish/update`, data })
  },
  // 查询已发布课程-pageList
  page: async (params) => {
    return await request.post({ url: `tb/course-publish/page`, data: params })
  },
  // 查询已发布课程-detail
  get: async (id) => {
    return await request.get({ url: `tb/course-publish/get?id=${id}` })
  },
  // 删除发布课程
  delete: async (id) => {
    return await request.delete({ url: `tb/course-publish/delete?id=${id}` })
  }
}
