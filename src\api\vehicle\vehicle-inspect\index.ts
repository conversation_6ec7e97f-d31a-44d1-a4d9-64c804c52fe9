import request from '@/config/axios'

interface VehicleInspectVO {
  licensePlate?: string // 车牌号
  vehicleName?: string // 车辆名称
  year?: number // 年份
  pageNo?: number // 页码
  pageSize?: number // 每页条数
}

// 点检类型
// オイル交換 maintenance
// 车辆修理 repair
// 3、12月点検 regular-inspection
// 车检 vehicle-inspection

interface InspectPlanVO {
  inspectDate: number // 点检日期
  inspectDateStr: string // 点检日期字符串
  inspectType: string // 点检类型
  checkDecember?: boolean // 是否检查12月
  appointer: string // 申请人
  status: string // 实施状态
  nickname: string // 实施人员
}

interface InspectImplVO {
  id: number //
  inspectDate: number // 点检日期
  inspectDateStr: string // 点检日期字符串
  inspectType: string // 点检类型
  checkDecember: boolean // 是否检查12月
  maintenanceId: string // 保养ID
  compensationPhotoList: string[] // 补偿照片列表
  appointmentTime: number // 预约时间
  appointer: string // 申请人
  status: string // 实施状态
  nickname: string // 实施人员
  maintenanceFiles: string[]
}

export interface VehicleInspectPageReqVO {
  vehicleId: string // 车辆ID
  licensePlate: string // 车牌号
  vehicleName: string // 车辆名称
  year: number // 年份
  inspectPlan: InspectPlanVO[] // 点检计划
  inspectImpl: InspectImplVO[] // 点检实施
}

// 车辆点检 API
export const VehicleInspectApi = {
  getVehicleInspectPage: async (
    params: VehicleInspectVO
  ): Promise<{
    list: VehicleInspectPageReqVO[]
    total: number
  }> => {
    return await request.post({ url: `/tb/vehicle-inspect/page`, data: params })
  }
}

export interface IAddInspectPlanVO {
  vehicleId: string
  inspectDate: number
  checkDecember: boolean
  inspectType: string
}
// 添加点检计划
export const addInspectPlan = async (params: IAddInspectPlanVO) => {
  return await request.post({ url: `/tb/vehicle-inspect/create`, data: params })
}

// 删除点检计划
export const delInspectPlan = async (id: number) => {
  return await request.delete({ url: `/tb/vehicle-inspect/delete?id=${id}` })
}

interface IAutoCalculateVO {
  scope: 'ALL' | 'SELECT'
  year: number
  vehicleIds: string[]
}

// 根据本年度最新12点检时间自动计算最近两年点检计划
export const autoCalculate = async (params: IAutoCalculateVO) => {
  return await request.put({ url: `/tb/vehicle-inspect/autoCalculate`, data: params })
}
