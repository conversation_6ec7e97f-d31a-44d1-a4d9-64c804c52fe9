import request from '@/config/axios'
import {
  VehicleMaintenanceConfigPageReqVO,
  VehicleMaintenanceConfigSaveReqVO,
  VehicleMaintenanceConfigBatchSaveReqVO
} from './vo'

// 车辆基础信息 API
export const MaintenanceConfigApi = {
  // 查询车辆预约配置分页
  getMaintenanceConfigPage: async (
    params: any
  ): Promise<PageResult<VehicleMaintenanceConfigPageReqVO[]>> => {
    return await request.post({ url: `/tb/vehicle/maintenance-config/page`, data: params })
  },

  // 批量修改车辆预约配置
  batchUpdateMaintenanceConfig: async (
    params: VehicleMaintenanceConfigBatchSaveReqVO
  ): Promise<void> => {
    return await request.post({ url: `/tb/vehicle-maintenance-config/batch-update`, data: params })
  },

  // 更新车辆预约配置
  updateMaintenanceConfig: async (params: VehicleMaintenanceConfigSaveReqVO): Promise<void> => {
    return await request.post({ url: `/tb/vehicle/maintenance-config/update`, data: params })
  },
  
  // 查询车辆预约配置详情
  getMaintenanceConfig: async (vehicleId: string): Promise<VehicleMaintenanceConfigPageReqVO> => {
    return await request.get({ url: `/tb/vehicle/maintenance-config/get?vehicleId=` + vehicleId })
  }
}
