import request from '@/config/axios'

export const OnlineLearningCourse = {
  // 课程计划树
  getCourseTree: async (params) => {
    return await request.get({ url: `tb/course/tree`, params })
  },
  // 创建课程
  createCourse: async (data) => {
    return await request.post({ url: `tb/course/create`, data })
  },
  // 删除课程
  deleteCourse: async (id) => {
    return await request.delete({ url: `tb/course/delete?id=${id}` })
  },
  // 修改课程
  updateCourse: async (data) => {
    return await request.put({ url: `tb/course/update`, data })
  },
  // 课程 分页
  getCoursePage: async (params) => {
    return await request.get({ url: `tb/course/page`, params })
  },
  // 查看下级
  nextLevelSuperClass: async (params) => {
    return await request.get({ url: `tb/course/nextLevel`, params })
  }
}
