import { UserVO } from '@/api/system/user'
import request from '@/config/axios'
import { formatDate } from '@/utils/formatTime'

// 点呼记录 VO
export interface RollCallRecordVO {
  id: number // 主键
  vehicleId: number // 车辆id
  vehicle: VehicleVO //车辆
  driverId: number // 驾驶员id
  driver: UserVO // 驾驶员
  rollCallDate: Date // 点呼日期
  confirmTime: Date // 确认时间
  rollCallMethod: string // 点呼方法
  rollCallResult: string // 点呼结果
  rollCallTiming: string // 点呼时机
  rollCallTime: Date // 点呼时间
  rollCallDateTime: Date // 点呼时间
  officeAddress: string // 营业所地址
  rollCallAddress: string // 点呼地址
  alcoholLevel: number // 酒精测量值 单位：mg/100ml
  hasAlcoholSmell: boolean // 驾驶员身上是否带有酒气
  temperature: number // 驾驶员体温
  callerUser: UserVO // callerUser
  genOpManagerUser: UserVO // genOpManagerUser
  opManagerUser: UserVO // opManagerUser
  assistUser: UserVO // assistUser
  mntManagerUser: UserVO, // mntManagerUser
  // 出库/归库时间
  outOrBackTime:Date
}

// 点呼记录 API
export const RollCallRecordApi = {
  // 查询点呼记录分页
  getRollCallRecordPage: async (params: any) => {
    return await request.get({ url: `/rollcall/roll-call-record/page`, params })
  },

  // 查询点呼记录详情
  getRollCallRecord: async (id: number) => {
    return await request.get({ url: `/rollcall/roll-call-record/get?id=` + id })
  },

  // 新增点呼记录
  createRollCallRecord: async (data: RollCallRecordVO) => {
    return await request.post({ url: `/rollcall/roll-call-record/create`, data })
  },

  // 修改点呼记录
  updateRollCallRecord: async (data: RollCallRecordVO) => {
    return await request.put({ url: `/rollcall/roll-call-record/update`, data })
  },

  // 删除点呼记录
  deleteRollCallRecord: async (id: number) => {
    return await request.delete({ url: `/rollcall/roll-call-record/delete?id=` + id })
  },

  // 导出点呼记录 Excel
  exportRollCallRecord: async (params) => {
    return await request.download({ url: `/rollcall/roll-call-record/export-excel`, params })
  },

  // 根据指定日期的日报生成点呼记录
  genRollCallRecordByDailyDate: async (dailyDate: Date) => {
    return await request.post({
      url: '/rollcall/roll-call-record/gen-roll-call-record',
      params: {
        dailyDate: formatDate(dailyDate, 'YYYY-MM-DD')
      }
    })
  },

  /**
   * 设置点呼记录管理者
   * @param rollCallIds 点呼记录ID列表
   * @param opManagerId 运营管理者用户id
   * @param assistId 辅助者用户id
   * @param mntManagerId 维修管理者用户id
   * @param genOpManagerId 运行管理者用户id
   * @returns 
   */
  setManager: async (
    rollCallIds: number[], // 点呼记录ID列表
    opManagerId: number, // 主管ID
    assistId: number, // 助理ID
    mntManagerId: number, // 维修主管ID
    genOpManagerId: number
  ) => {
    return await request.put({
      url: '/rollcall/roll-call-record/set-manager',
      data: {
        rollCallIds,
        opManagerId,
        assistId,
        mntManagerId,
        genOpManagerId
      }
    })
  }
}
