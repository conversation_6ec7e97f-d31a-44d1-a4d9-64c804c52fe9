import request from '@/config/axios'

// 店铺信息 VO
export interface ShopInfoVO {
  id: number // 主键ID
  shopName: string // 店铺名称
  shopType: string // 店铺类型
  address: string // 详细地址
  phone: string // 联系电话
  postalCode: string // 邮政编码
  email: string // 电子邮箱
  mapLocation: string // 导航地址（如地图链接或坐标）
  businessHours: string // 营业时间
  contactPerson: string // 联系人
}

// 店铺信息 API
export const ShopInfoApi = {
  // 查询店铺信息分页
  getShopInfoPage: async (params: any) => {
    return await request.get({ url: `/tb/shop-info/page`, params })
  },

  // 查询店铺信息详情
  getShopInfo: async (id: number) => {
    return await request.get({ url: `/tb/shop-info/get?id=` + id })
  },

  // 新增店铺信息
  createShopInfo: async (data: ShopInfoVO) => {
    return await request.post({ url: `/tb/shop-info/create`, data })
  },

  // 修改店铺信息
  updateShopInfo: async (data: ShopInfoVO) => {
    return await request.put({ url: `/tb/shop-info/update`, data })
  },

  // 删除店铺信息
  deleteShopInfo: async (id: number) => {
    return await request.delete({ url: `/tb/shop-info/delete?id=` + id })
  },

  // 导出店铺信息 Excel
  exportShopInfo: async (params) => {
    return await request.download({ url: `/tb/shop-info/export-excel`, params })
  }
}